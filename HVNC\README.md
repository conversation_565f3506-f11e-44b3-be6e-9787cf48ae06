# HVNC - Tinynuke (Fixed)
This HVNC Client and Server is based off of the Tinynuke botnet's HVNC (C++).

I do **NOT** encourage malicious use of this code. This was made for educational purposes only.

Credits: https://github.com/rossja/TinyNuke

# Features:
- Start Explorer (Hidden Desktop)
- Open "Run"
- Start Powershell
- Start Chrome
- Start Edge
- Start Brave
- Start Firefox
- Start Internet Explorer

# Usage:
- In the Client's "Main.cpp" file, edit the ip and port variables.
- Compile the Server & Client, and run the Server. 
- Enter the port to listen on in the Server's console when prompted.
- When the Client is executed, it will open a new "Hidden Desktop" window. If you right-click on the white bar at the top of the "Hidden Desktop" window, you can view the available commands that you can run on the target machine.

# Updates (New):

- Fixed Browser Data Clone
- Added "Start Powershell" Option
- Made Client Console Hidden
- Added "Start Edge" Option
- Added "Start Brave" Option
- Adjusted Window Size for "Start Powershell"
- Added Prompt for Port to Listen On

# Demo of HVNC Window:
View Demo Video: https://vimeo.com/597459719

![Image1](https://i.ibb.co/JxMn3j4/image.png)

# Contact Me:
Discord: melted3294
